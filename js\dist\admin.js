(function(w,je,rt,we,<PERSON>,<PERSON>e,Ft,at){"use strict";const F=window.flarum?.core?.compat?.mithril||window.m,ve=class ve extends we{oninit(t){super.oninit(t),this.ButtonsCustomizationItemData=this.attrs.ButtonsCustomizationItemData,this.settingType="add",this.loading=!1,this.ButtonsCustomizationItemData?(this.settingType="edit",this.itemName=J(this.ButtonsCustomizationItemData.name()),this.itemUrl=J(this.ButtonsCustomizationItemData.url()),this.itemIcon=J(this.ButtonsCustomizationItemData.icon()),this.itemColor=J(this.ButtonsCustomizationItemData.color())):(this.itemName=J(""),this.itemUrl=J(""),this.itemIcon=J(""),this.itemColor=J(""))}className(){return"Modal--medium"}title(){return this.settingType==="add"?w.translator.trans("client1-buttons-customization.admin.settings.item-add"):w.translator.trans("client1-buttons-customization.admin.settings.item-edit")}content(){return F("div",{className:"Modal-body"},[F("div",{className:"Form"},[F("div",{className:"Form-group",style:"text-align: center;"},[F("div",[F("div",{className:"GuaGuaLeSettingsLabel"},w.translator.trans("client1-buttons-customization.admin.settings.item-name")),F("input",{maxlength:255,required:!0,className:"FormControl",value:this.itemName(),oninput:t=>this.itemName(t.target.value)}),F("div",{className:"GuaGuaLeSettingsLabel"},w.translator.trans("client1-buttons-customization.admin.settings.item-url")),F("input",{maxlength:500,required:!0,className:"FormControl",value:this.itemUrl(),oninput:t=>this.itemUrl(t.target.value)}),F("div",{className:"GuaGuaLeSettingsLabel"},w.translator.trans("client1-buttons-customization.admin.settings.item-icon")),F("input",{maxlength:50,required:!0,className:"FormControl",value:this.itemIcon(),oninput:t=>this.itemIcon(t.target.value)})])]),F("div",{className:"Form-group",style:"text-align: center;"},[F(rt,{className:"Button Button--primary",type:"submit",loading:this.loading},w.translator.trans("client1-buttons-customization.admin.confirm"))," ",F(rt,{className:"Button guagualeButton--gray",loading:this.loading,onclick:()=>this.hide()},w.translator.trans("client1-buttons-customization.admin.cancel"))])])])}onsubmit(t){t.preventDefault(),this.loading=!0,this.settingType==="edit"&&this.ButtonsCustomizationItemData?this.ButtonsCustomizationItemData.save({name:this.itemName(),url:this.itemUrl(),icon:this.itemIcon(),color:this.itemColor()}).then(()=>this.hide()).catch(e=>{this.loading=!1,this.handleErrors(e)}):w.store.createRecord("buttonsCustomizationList").save({name:this.itemName(),url:this.itemUrl(),icon:this.itemIcon(),color:this.itemColor()}).then(()=>{location.reload()}).catch(e=>{this.loading=!1,this.handleErrors(e)})}handleErrors(t){console.error("ButtonsCustomizationAddModal error",t),w.alerts.show({type:"error"},w.translator.trans("client1-buttons-customization.admin.save-error"))}};ve.isDismissible=!1;let Mt=ve;const Bt=window.flarum?.core?.compat?.mithril||window.m,be=class be extends we{oninit(t){super.oninit(t),this.ButtonsCustomizationItemData=this.attrs.ButtonsCustomizationItemData,this.loading=!1}className(){return"Modal--small"}title(){return w.translator.trans("client1-buttons-customization.admin.settings.item-delete-confirmation")}content(){return Bt("div",{className:"Modal-body"},[Bt("div",{className:"Form-group",style:"text-align: center;"},[Bt(rt,{className:"Button Button--primary",type:"submit",loading:this.loading},w.translator.trans("client1-buttons-customization.admin.confirm"))," ",Bt(rt,{className:"Button guagualeButton--gray",loading:this.loading,onclick:()=>this.hide()},w.translator.trans("client1-buttons-customization.admin.cancel"))])])}onsubmit(t){t.preventDefault(),this.loading=!0,this.ButtonsCustomizationItemData.delete().then(()=>{location.reload()})}};be.isDismissible=!1;let Jt=be;const q=window.flarum?.core?.compat?.mithril||window.m;class qe extends Ue{view(){const{ButtonsCustomizationItemData:t}=this.attrs,e=t.id(),n=t.name(),o=t.url(),r=t.icon();return q("div",{style:"border: 1px dotted var(--control-color);padding: 10px;border-radius: 4px;"},[q("div",[q("div",{style:"padding-top: 5px;"},[q(rt,{className:"Button Button--primary",onclick:()=>this.editItem(t)},w.translator.trans("client1-buttons-customization.admin.settings.item-edit"))," ",q(rt,{className:"Button Button--danger",style:"font-weight:bold;width:66px;",onclick:()=>this.deleteItem(t)},w.translator.trans("client1-buttons-customization.admin.settings.item-delete"))," ",q("b",w.translator.trans("client1-buttons-customization.admin.settings.item-id")+": "),e," | ",q("i",{className:r})," ",q("b",w.translator.trans("client1-buttons-customization.admin.settings.item-name")+": "),n," | ",q("b",w.translator.trans("client1-buttons-customization.admin.settings.item-url")+": "),o," "])])])}editItem(t){w.modal.show(Mt,{ButtonsCustomizationItemData:t})}deleteItem(t){w.modal.show(Jt,{ButtonsCustomizationItemData:t})}}/**!
 * Sortable 1.15.6
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function ye(i,t){var e=Object.keys(i);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(i);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(i,o).enumerable})),e.push.apply(e,n)}return e}function W(i){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?ye(Object(e),!0).forEach(function(n){$e(i,n,e[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(e)):ye(Object(e)).forEach(function(n){Object.defineProperty(i,n,Object.getOwnPropertyDescriptor(e,n))})}return i}function Rt(i){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Rt=function(t){return typeof t}:Rt=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rt(i)}function $e(i,t,e){return t in i?Object.defineProperty(i,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):i[t]=e,i}function $(){return $=Object.assign||function(i){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(i[n]=e[n])}return i},$.apply(this,arguments)}function Ve(i,t){if(i==null)return{};var e={},n=Object.keys(i),o,r;for(r=0;r<n.length;r++)o=n[r],!(t.indexOf(o)>=0)&&(e[o]=i[o]);return e}function Ke(i,t){if(i==null)return{};var e=Ve(i,t),n,o;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(i);for(o=0;o<r.length;o++)n=r[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(i,n)&&(e[n]=i[n])}return e}var Ze="1.15.6";function V(i){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(i)}var K=V(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),wt=V(/Edge/i),Ee=V(/firefox/i),yt=V(/safari/i)&&!V(/chrome/i)&&!V(/android/i),te=V(/iP(ad|od|hone)/i),De=V(/chrome/i)&&V(/android/i),_e={capture:!1,passive:!1};function v(i,t,e){i.addEventListener(t,e,!K&&_e)}function p(i,t,e){i.removeEventListener(t,e,!K&&_e)}function kt(i,t){if(t){if(t[0]===">"&&(t=t.substring(1)),i)try{if(i.matches)return i.matches(t);if(i.msMatchesSelector)return i.msMatchesSelector(t);if(i.webkitMatchesSelector)return i.webkitMatchesSelector(t)}catch{return!1}return!1}}function Se(i){return i.host&&i!==document&&i.host.nodeType?i.host:i.parentNode}function Y(i,t,e,n){if(i){e=e||document;do{if(t!=null&&(t[0]===">"?i.parentNode===e&&kt(i,t):kt(i,t))||n&&i===e)return i;if(i===e)break}while(i=Se(i))}return null}var Ce=/\s+/g;function M(i,t,e){if(i&&t)if(i.classList)i.classList[e?"add":"remove"](t);else{var n=(" "+i.className+" ").replace(Ce," ").replace(" "+t+" "," ");i.className=(n+(e?" "+t:"")).replace(Ce," ")}}function h(i,t,e){var n=i&&i.style;if(n){if(e===void 0)return document.defaultView&&document.defaultView.getComputedStyle?e=document.defaultView.getComputedStyle(i,""):i.currentStyle&&(e=i.currentStyle),t===void 0?e:e[t];!(t in n)&&t.indexOf("webkit")===-1&&(t="-webkit-"+t),n[t]=e+(typeof e=="string"?"":"px")}}function ht(i,t){var e="";if(typeof i=="string")e=i;else do{var n=h(i,"transform");n&&n!=="none"&&(e=n+" "+e)}while(!t&&(i=i.parentNode));var o=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return o&&new o(e)}function Te(i,t,e){if(i){var n=i.getElementsByTagName(t),o=0,r=n.length;if(e)for(;o<r;o++)e(n[o],o);return n}return[]}function j(){var i=document.scrollingElement;return i||document.documentElement}function T(i,t,e,n,o){if(!(!i.getBoundingClientRect&&i!==window)){var r,a,s,l,u,f,d;if(i!==window&&i.parentNode&&i!==j()?(r=i.getBoundingClientRect(),a=r.top,s=r.left,l=r.bottom,u=r.right,f=r.height,d=r.width):(a=0,s=0,l=window.innerHeight,u=window.innerWidth,f=window.innerHeight,d=window.innerWidth),(t||e)&&i!==window&&(o=o||i.parentNode,!K))do if(o&&o.getBoundingClientRect&&(h(o,"transform")!=="none"||e&&h(o,"position")!=="static")){var b=o.getBoundingClientRect();a-=b.top+parseInt(h(o,"border-top-width")),s-=b.left+parseInt(h(o,"border-left-width")),l=a+r.height,u=s+r.width;break}while(o=o.parentNode);if(n&&i!==window){var D=ht(o||i),y=D&&D.a,E=D&&D.d;D&&(a/=E,s/=y,d/=y,f/=E,l=a+f,u=s+d)}return{top:a,left:s,bottom:l,right:u,width:d,height:f}}}function Ie(i,t,e){for(var n=tt(i,!0),o=T(i)[t];n;){var r=T(n)[e],a=void 0;if(a=o>=r,!a)return n;if(n===j())break;n=tt(n,!1)}return!1}function mt(i,t,e,n){for(var o=0,r=0,a=i.children;r<a.length;){if(a[r].style.display!=="none"&&a[r]!==m.ghost&&(n||a[r]!==m.dragged)&&Y(a[r],e.draggable,i,!1)){if(o===t)return a[r];o++}r++}return null}function ee(i,t){for(var e=i.lastElementChild;e&&(e===m.ghost||h(e,"display")==="none"||t&&!kt(e,t));)e=e.previousElementSibling;return e||null}function X(i,t){var e=0;if(!i||!i.parentNode)return-1;for(;i=i.previousElementSibling;)i.nodeName.toUpperCase()!=="TEMPLATE"&&i!==m.clone&&(!t||kt(i,t))&&e++;return e}function Ne(i){var t=0,e=0,n=j();if(i)do{var o=ht(i),r=o.a,a=o.d;t+=i.scrollLeft*r,e+=i.scrollTop*a}while(i!==n&&(i=i.parentNode));return[t,e]}function Qe(i,t){for(var e in i)if(i.hasOwnProperty(e)){for(var n in t)if(t.hasOwnProperty(n)&&t[n]===i[e][n])return Number(e)}return-1}function tt(i,t){if(!i||!i.getBoundingClientRect)return j();var e=i,n=!1;do if(e.clientWidth<e.scrollWidth||e.clientHeight<e.scrollHeight){var o=h(e);if(e.clientWidth<e.scrollWidth&&(o.overflowX=="auto"||o.overflowX=="scroll")||e.clientHeight<e.scrollHeight&&(o.overflowY=="auto"||o.overflowY=="scroll")){if(!e.getBoundingClientRect||e===document.body)return j();if(n||t)return e;n=!0}}while(e=e.parentNode);return j()}function Je(i,t){if(i&&t)for(var e in t)t.hasOwnProperty(e)&&(i[e]=t[e]);return i}function ne(i,t){return Math.round(i.top)===Math.round(t.top)&&Math.round(i.left)===Math.round(t.left)&&Math.round(i.height)===Math.round(t.height)&&Math.round(i.width)===Math.round(t.width)}var Et;function Oe(i,t){return function(){if(!Et){var e=arguments,n=this;e.length===1?i.call(n,e[0]):i.apply(n,e),Et=setTimeout(function(){Et=void 0},t)}}}function tn(){clearTimeout(Et),Et=void 0}function xe(i,t,e){i.scrollLeft+=t,i.scrollTop+=e}function Pe(i){var t=window.Polymer,e=window.jQuery||window.Zepto;return t&&t.dom?t.dom(i).cloneNode(!0):e?e(i).clone(!0)[0]:i.cloneNode(!0)}function Ae(i,t,e){var n={};return Array.from(i.children).forEach(function(o){var r,a,s,l;if(!(!Y(o,t.draggable,i,!1)||o.animated||o===e)){var u=T(o);n.left=Math.min((r=n.left)!==null&&r!==void 0?r:1/0,u.left),n.top=Math.min((a=n.top)!==null&&a!==void 0?a:1/0,u.top),n.right=Math.max((s=n.right)!==null&&s!==void 0?s:-1/0,u.right),n.bottom=Math.max((l=n.bottom)!==null&&l!==void 0?l:-1/0,u.bottom)}}),n.width=n.right-n.left,n.height=n.bottom-n.top,n.x=n.left,n.y=n.top,n}var A="Sortable"+new Date().getTime();function en(){var i=[],t;return{captureAnimationState:function(){if(i=[],!!this.options.animation){var n=[].slice.call(this.el.children);n.forEach(function(o){if(!(h(o,"display")==="none"||o===m.ghost)){i.push({target:o,rect:T(o)});var r=W({},i[i.length-1].rect);if(o.thisAnimationDuration){var a=ht(o,!0);a&&(r.top-=a.f,r.left-=a.e)}o.fromRect=r}})}},addAnimationState:function(n){i.push(n)},removeAnimationState:function(n){i.splice(Qe(i,{target:n}),1)},animateAll:function(n){var o=this;if(!this.options.animation){clearTimeout(t),typeof n=="function"&&n();return}var r=!1,a=0;i.forEach(function(s){var l=0,u=s.target,f=u.fromRect,d=T(u),b=u.prevFromRect,D=u.prevToRect,y=s.rect,E=ht(u,!0);E&&(d.top-=E.f,d.left-=E.e),u.toRect=d,u.thisAnimationDuration&&ne(b,d)&&!ne(f,d)&&(y.top-d.top)/(y.left-d.left)===(f.top-d.top)/(f.left-d.left)&&(l=on(y,b,D,o.options)),ne(d,f)||(u.prevFromRect=f,u.prevToRect=d,l||(l=o.options.animation),o.animate(u,y,d,l)),l&&(r=!0,a=Math.max(a,l),clearTimeout(u.animationResetTimer),u.animationResetTimer=setTimeout(function(){u.animationTime=0,u.prevFromRect=null,u.fromRect=null,u.prevToRect=null,u.thisAnimationDuration=null},l),u.thisAnimationDuration=l)}),clearTimeout(t),r?t=setTimeout(function(){typeof n=="function"&&n()},a):typeof n=="function"&&n(),i=[]},animate:function(n,o,r,a){if(a){h(n,"transition",""),h(n,"transform","");var s=ht(this.el),l=s&&s.a,u=s&&s.d,f=(o.left-r.left)/(l||1),d=(o.top-r.top)/(u||1);n.animatingX=!!f,n.animatingY=!!d,h(n,"transform","translate3d("+f+"px,"+d+"px,0)"),this.forRepaintDummy=nn(n),h(n,"transition","transform "+a+"ms"+(this.options.easing?" "+this.options.easing:"")),h(n,"transform","translate3d(0,0,0)"),typeof n.animated=="number"&&clearTimeout(n.animated),n.animated=setTimeout(function(){h(n,"transition",""),h(n,"transform",""),n.animated=!1,n.animatingX=!1,n.animatingY=!1},a)}}}}function nn(i){return i.offsetWidth}function on(i,t,e,n){return Math.sqrt(Math.pow(t.top-i.top,2)+Math.pow(t.left-i.left,2))/Math.sqrt(Math.pow(t.top-e.top,2)+Math.pow(t.left-e.left,2))*n.animation}var gt=[],ie={initializeByDefault:!0},Dt={mount:function(t){for(var e in ie)ie.hasOwnProperty(e)&&!(e in t)&&(t[e]=ie[e]);gt.forEach(function(n){if(n.pluginName===t.pluginName)throw"Sortable: Cannot mount plugin ".concat(t.pluginName," more than once")}),gt.push(t)},pluginEvent:function(t,e,n){var o=this;this.eventCanceled=!1,n.cancel=function(){o.eventCanceled=!0};var r=t+"Global";gt.forEach(function(a){e[a.pluginName]&&(e[a.pluginName][r]&&e[a.pluginName][r](W({sortable:e},n)),e.options[a.pluginName]&&e[a.pluginName][t]&&e[a.pluginName][t](W({sortable:e},n)))})},initializePlugins:function(t,e,n,o){gt.forEach(function(s){var l=s.pluginName;if(!(!t.options[l]&&!s.initializeByDefault)){var u=new s(t,e,t.options);u.sortable=t,u.options=t.options,t[l]=u,$(n,u.defaults)}});for(var r in t.options)if(t.options.hasOwnProperty(r)){var a=this.modifyOption(t,r,t.options[r]);typeof a<"u"&&(t.options[r]=a)}},getEventProperties:function(t,e){var n={};return gt.forEach(function(o){typeof o.eventProperties=="function"&&$(n,o.eventProperties.call(e[o.pluginName],t))}),n},modifyOption:function(t,e,n){var o;return gt.forEach(function(r){t[r.pluginName]&&r.optionListeners&&typeof r.optionListeners[e]=="function"&&(o=r.optionListeners[e].call(t[r.pluginName],n))}),o}};function rn(i){var t=i.sortable,e=i.rootEl,n=i.name,o=i.targetEl,r=i.cloneEl,a=i.toEl,s=i.fromEl,l=i.oldIndex,u=i.newIndex,f=i.oldDraggableIndex,d=i.newDraggableIndex,b=i.originalEvent,D=i.putSortable,y=i.extraEventProperties;if(t=t||e&&e[A],!!t){var E,G=t.options,Z="on"+n.charAt(0).toUpperCase()+n.substr(1);window.CustomEvent&&!K&&!wt?E=new CustomEvent(n,{bubbles:!0,cancelable:!0}):(E=document.createEvent("Event"),E.initEvent(n,!0,!0)),E.to=a||e,E.from=s||e,E.item=o||e,E.clone=r,E.oldIndex=l,E.newIndex=u,E.oldDraggableIndex=f,E.newDraggableIndex=d,E.originalEvent=b,E.pullMode=D?D.lastPutMode:void 0;var P=W(W({},y),Dt.getEventProperties(n,t));for(var H in P)E[H]=P[H];e&&e.dispatchEvent(E),G[Z]&&G[Z].call(t,E)}}var an=["evt"],z=function(t,e){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},o=n.evt,r=Ke(n,an);Dt.pluginEvent.bind(m)(t,e,W({dragEl:c,parentEl:C,ghostEl:g,rootEl:_,nextEl:st,lastDownEl:Xt,cloneEl:S,cloneHidden:et,dragStarted:St,putSortable:N,activeSortable:m.active,originalEvent:o,oldIndex:pt,oldDraggableIndex:_t,newIndex:B,newDraggableIndex:nt,hideGhostForTarget:Xe,unhideGhostForTarget:Ye,cloneNowHidden:function(){et=!0},cloneNowShown:function(){et=!1},dispatchSortableEvent:function(s){x({sortable:e,name:s,originalEvent:o})}},r))};function x(i){rn(W({putSortable:N,cloneEl:S,targetEl:c,rootEl:_,oldIndex:pt,oldDraggableIndex:_t,newIndex:B,newDraggableIndex:nt},i))}var c,C,g,_,st,Xt,S,et,pt,B,_t,nt,Yt,N,vt=!1,Lt=!1,Gt=[],lt,L,oe,re,ze,Fe,St,bt,Ct,Tt=!1,Ht=!1,Wt,O,ae=[],se=!1,jt=[],Ut=typeof document<"u",qt=te,Me=wt||K?"cssFloat":"float",sn=Ut&&!De&&!te&&"draggable"in document.createElement("div"),Be=(function(){if(Ut){if(K)return!1;var i=document.createElement("x");return i.style.cssText="pointer-events:auto",i.style.pointerEvents==="auto"}})(),Re=function(t,e){var n=h(t),o=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),r=mt(t,0,e),a=mt(t,1,e),s=r&&h(r),l=a&&h(a),u=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+T(r).width,f=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+T(a).width;if(n.display==="flex")return n.flexDirection==="column"||n.flexDirection==="column-reverse"?"vertical":"horizontal";if(n.display==="grid")return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(r&&s.float&&s.float!=="none"){var d=s.float==="left"?"left":"right";return a&&(l.clear==="both"||l.clear===d)?"vertical":"horizontal"}return r&&(s.display==="block"||s.display==="flex"||s.display==="table"||s.display==="grid"||u>=o&&n[Me]==="none"||a&&n[Me]==="none"&&u+f>o)?"vertical":"horizontal"},ln=function(t,e,n){var o=n?t.left:t.top,r=n?t.right:t.bottom,a=n?t.width:t.height,s=n?e.left:e.top,l=n?e.right:e.bottom,u=n?e.width:e.height;return o===s||r===l||o+a/2===s+u/2},un=function(t,e){var n;return Gt.some(function(o){var r=o[A].options.emptyInsertThreshold;if(!(!r||ee(o))){var a=T(o),s=t>=a.left-r&&t<=a.right+r,l=e>=a.top-r&&e<=a.bottom+r;if(s&&l)return n=o}}),n},ke=function(t){function e(r,a){return function(s,l,u,f){var d=s.options.group.name&&l.options.group.name&&s.options.group.name===l.options.group.name;if(r==null&&(a||d))return!0;if(r==null||r===!1)return!1;if(a&&r==="clone")return r;if(typeof r=="function")return e(r(s,l,u,f),a)(s,l,u,f);var b=(a?s:l).options.group.name;return r===!0||typeof r=="string"&&r===b||r.join&&r.indexOf(b)>-1}}var n={},o=t.group;(!o||Rt(o)!="object")&&(o={name:o}),n.name=o.name,n.checkPull=e(o.pull,!0),n.checkPut=e(o.put),n.revertClone=o.revertClone,t.group=n},Xe=function(){!Be&&g&&h(g,"display","none")},Ye=function(){!Be&&g&&h(g,"display","")};Ut&&!De&&document.addEventListener("click",function(i){if(Lt)return i.preventDefault(),i.stopPropagation&&i.stopPropagation(),i.stopImmediatePropagation&&i.stopImmediatePropagation(),Lt=!1,!1},!0);var ut=function(t){if(c){t=t.touches?t.touches[0]:t;var e=un(t.clientX,t.clientY);if(e){var n={};for(var o in t)t.hasOwnProperty(o)&&(n[o]=t[o]);n.target=n.rootEl=e,n.preventDefault=void 0,n.stopPropagation=void 0,e[A]._onDragOver(n)}}},cn=function(t){c&&c.parentNode[A]._isOutsideThisEl(t.target)};function m(i,t){if(!(i&&i.nodeType&&i.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(i));this.el=i,this.options=t=$({},t),i[A]=this;var e={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(i.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Re(i,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(a,s){a.setData("Text",s.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:m.supportPointer!==!1&&"PointerEvent"in window&&(!yt||te),emptyInsertThreshold:5};Dt.initializePlugins(this,i,e);for(var n in e)!(n in t)&&(t[n]=e[n]);ke(t);for(var o in this)o.charAt(0)==="_"&&typeof this[o]=="function"&&(this[o]=this[o].bind(this));this.nativeDraggable=t.forceFallback?!1:sn,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?v(i,"pointerdown",this._onTapStart):(v(i,"mousedown",this._onTapStart),v(i,"touchstart",this._onTapStart)),this.nativeDraggable&&(v(i,"dragover",this),v(i,"dragenter",this)),Gt.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),$(this,en())}m.prototype={constructor:m,_isOutsideThisEl:function(t){!this.el.contains(t)&&t!==this.el&&(bt=null)},_getDirection:function(t,e){return typeof this.options.direction=="function"?this.options.direction.call(this,t,e,c):this.options.direction},_onTapStart:function(t){if(t.cancelable){var e=this,n=this.el,o=this.options,r=o.preventOnFilter,a=t.type,s=t.touches&&t.touches[0]||t.pointerType&&t.pointerType==="touch"&&t,l=(s||t).target,u=t.target.shadowRoot&&(t.path&&t.path[0]||t.composedPath&&t.composedPath()[0])||l,f=o.filter;if(bn(n),!c&&!(/mousedown|pointerdown/.test(a)&&t.button!==0||o.disabled)&&!u.isContentEditable&&!(!this.nativeDraggable&&yt&&l&&l.tagName.toUpperCase()==="SELECT")&&(l=Y(l,o.draggable,n,!1),!(l&&l.animated)&&Xt!==l)){if(pt=X(l),_t=X(l,o.draggable),typeof f=="function"){if(f.call(this,t,l,this)){x({sortable:e,rootEl:u,name:"filter",targetEl:l,toEl:n,fromEl:n}),z("filter",e,{evt:t}),r&&t.preventDefault();return}}else if(f&&(f=f.split(",").some(function(d){if(d=Y(u,d.trim(),n,!1),d)return x({sortable:e,rootEl:d,name:"filter",targetEl:l,fromEl:n,toEl:n}),z("filter",e,{evt:t}),!0}),f)){r&&t.preventDefault();return}o.handle&&!Y(u,o.handle,n,!1)||this._prepareDragStart(t,s,l)}}},_prepareDragStart:function(t,e,n){var o=this,r=o.el,a=o.options,s=r.ownerDocument,l;if(n&&!c&&n.parentNode===r){var u=T(n);if(_=r,c=n,C=c.parentNode,st=c.nextSibling,Xt=n,Yt=a.group,m.dragged=c,lt={target:c,clientX:(e||t).clientX,clientY:(e||t).clientY},ze=lt.clientX-u.left,Fe=lt.clientY-u.top,this._lastX=(e||t).clientX,this._lastY=(e||t).clientY,c.style["will-change"]="all",l=function(){if(z("delayEnded",o,{evt:t}),m.eventCanceled){o._onDrop();return}o._disableDelayedDragEvents(),!Ee&&o.nativeDraggable&&(c.draggable=!0),o._triggerDragStart(t,e),x({sortable:o,name:"choose",originalEvent:t}),M(c,a.chosenClass,!0)},a.ignore.split(",").forEach(function(f){Te(c,f.trim(),le)}),v(s,"dragover",ut),v(s,"mousemove",ut),v(s,"touchmove",ut),a.supportPointer?(v(s,"pointerup",o._onDrop),!this.nativeDraggable&&v(s,"pointercancel",o._onDrop)):(v(s,"mouseup",o._onDrop),v(s,"touchend",o._onDrop),v(s,"touchcancel",o._onDrop)),Ee&&this.nativeDraggable&&(this.options.touchStartThreshold=4,c.draggable=!0),z("delayStart",this,{evt:t}),a.delay&&(!a.delayOnTouchOnly||e)&&(!this.nativeDraggable||!(wt||K))){if(m.eventCanceled){this._onDrop();return}a.supportPointer?(v(s,"pointerup",o._disableDelayedDrag),v(s,"pointercancel",o._disableDelayedDrag)):(v(s,"mouseup",o._disableDelayedDrag),v(s,"touchend",o._disableDelayedDrag),v(s,"touchcancel",o._disableDelayedDrag)),v(s,"mousemove",o._delayedDragTouchMoveHandler),v(s,"touchmove",o._delayedDragTouchMoveHandler),a.supportPointer&&v(s,"pointermove",o._delayedDragTouchMoveHandler),o._dragStartTimer=setTimeout(l,a.delay)}else l()}},_delayedDragTouchMoveHandler:function(t){var e=t.touches?t.touches[0]:t;Math.max(Math.abs(e.clientX-this._lastX),Math.abs(e.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){c&&le(c),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var t=this.el.ownerDocument;p(t,"mouseup",this._disableDelayedDrag),p(t,"touchend",this._disableDelayedDrag),p(t,"touchcancel",this._disableDelayedDrag),p(t,"pointerup",this._disableDelayedDrag),p(t,"pointercancel",this._disableDelayedDrag),p(t,"mousemove",this._delayedDragTouchMoveHandler),p(t,"touchmove",this._delayedDragTouchMoveHandler),p(t,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(t,e){e=e||t.pointerType=="touch"&&t,!this.nativeDraggable||e?this.options.supportPointer?v(document,"pointermove",this._onTouchMove):e?v(document,"touchmove",this._onTouchMove):v(document,"mousemove",this._onTouchMove):(v(c,"dragend",this),v(_,"dragstart",this._onDragStart));try{document.selection?Vt(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(t,e){if(vt=!1,_&&c){z("dragStarted",this,{evt:e}),this.nativeDraggable&&v(document,"dragover",cn);var n=this.options;!t&&M(c,n.dragClass,!1),M(c,n.ghostClass,!0),m.active=this,t&&this._appendGhost(),x({sortable:this,name:"start",originalEvent:e})}else this._nulling()},_emulateDragOver:function(){if(L){this._lastX=L.clientX,this._lastY=L.clientY,Xe();for(var t=document.elementFromPoint(L.clientX,L.clientY),e=t;t&&t.shadowRoot&&(t=t.shadowRoot.elementFromPoint(L.clientX,L.clientY),t!==e);)e=t;if(c.parentNode[A]._isOutsideThisEl(t),e)do{if(e[A]){var n=void 0;if(n=e[A]._onDragOver({clientX:L.clientX,clientY:L.clientY,target:t,rootEl:e}),n&&!this.options.dragoverBubble)break}t=e}while(e=Se(e));Ye()}},_onTouchMove:function(t){if(lt){var e=this.options,n=e.fallbackTolerance,o=e.fallbackOffset,r=t.touches?t.touches[0]:t,a=g&&ht(g,!0),s=g&&a&&a.a,l=g&&a&&a.d,u=qt&&O&&Ne(O),f=(r.clientX-lt.clientX+o.x)/(s||1)+(u?u[0]-ae[0]:0)/(s||1),d=(r.clientY-lt.clientY+o.y)/(l||1)+(u?u[1]-ae[1]:0)/(l||1);if(!m.active&&!vt){if(n&&Math.max(Math.abs(r.clientX-this._lastX),Math.abs(r.clientY-this._lastY))<n)return;this._onDragStart(t,!0)}if(g){a?(a.e+=f-(oe||0),a.f+=d-(re||0)):a={a:1,b:0,c:0,d:1,e:f,f:d};var b="matrix(".concat(a.a,",").concat(a.b,",").concat(a.c,",").concat(a.d,",").concat(a.e,",").concat(a.f,")");h(g,"webkitTransform",b),h(g,"mozTransform",b),h(g,"msTransform",b),h(g,"transform",b),oe=f,re=d,L=r}t.cancelable&&t.preventDefault()}},_appendGhost:function(){if(!g){var t=this.options.fallbackOnBody?document.body:_,e=T(c,!0,qt,!0,t),n=this.options;if(qt){for(O=t;h(O,"position")==="static"&&h(O,"transform")==="none"&&O!==document;)O=O.parentNode;O!==document.body&&O!==document.documentElement?(O===document&&(O=j()),e.top+=O.scrollTop,e.left+=O.scrollLeft):O=j(),ae=Ne(O)}g=c.cloneNode(!0),M(g,n.ghostClass,!1),M(g,n.fallbackClass,!0),M(g,n.dragClass,!0),h(g,"transition",""),h(g,"transform",""),h(g,"box-sizing","border-box"),h(g,"margin",0),h(g,"top",e.top),h(g,"left",e.left),h(g,"width",e.width),h(g,"height",e.height),h(g,"opacity","0.8"),h(g,"position",qt?"absolute":"fixed"),h(g,"zIndex","100000"),h(g,"pointerEvents","none"),m.ghost=g,t.appendChild(g),h(g,"transform-origin",ze/parseInt(g.style.width)*100+"% "+Fe/parseInt(g.style.height)*100+"%")}},_onDragStart:function(t,e){var n=this,o=t.dataTransfer,r=n.options;if(z("dragStart",this,{evt:t}),m.eventCanceled){this._onDrop();return}z("setupClone",this),m.eventCanceled||(S=Pe(c),S.removeAttribute("id"),S.draggable=!1,S.style["will-change"]="",this._hideClone(),M(S,this.options.chosenClass,!1),m.clone=S),n.cloneId=Vt(function(){z("clone",n),!m.eventCanceled&&(n.options.removeCloneOnHide||_.insertBefore(S,c),n._hideClone(),x({sortable:n,name:"clone"}))}),!e&&M(c,r.dragClass,!0),e?(Lt=!0,n._loopId=setInterval(n._emulateDragOver,50)):(p(document,"mouseup",n._onDrop),p(document,"touchend",n._onDrop),p(document,"touchcancel",n._onDrop),o&&(o.effectAllowed="move",r.setData&&r.setData.call(n,o,c)),v(document,"drop",n),h(c,"transform","translateZ(0)")),vt=!0,n._dragStartId=Vt(n._dragStarted.bind(n,e,t)),v(document,"selectstart",n),St=!0,window.getSelection().removeAllRanges(),yt&&h(document.body,"user-select","none")},_onDragOver:function(t){var e=this.el,n=t.target,o,r,a,s=this.options,l=s.group,u=m.active,f=Yt===l,d=s.sort,b=N||u,D,y=this,E=!1;if(se)return;function G(zt,En){z(zt,y,W({evt:t,isOwner:f,axis:D?"vertical":"horizontal",revert:a,dragRect:o,targetRect:r,canSort:d,fromSortable:b,target:n,completed:P,onMove:function(We,Dn){return $t(_,e,c,o,We,T(We),t,Dn)},changed:H},En))}function Z(){G("dragOverAnimationCapture"),y.captureAnimationState(),y!==b&&b.captureAnimationState()}function P(zt){return G("dragOverCompleted",{insertion:zt}),zt&&(f?u._hideClone():u._showClone(y),y!==b&&(M(c,N?N.options.ghostClass:u.options.ghostClass,!1),M(c,s.ghostClass,!0)),N!==y&&y!==m.active?N=y:y===m.active&&N&&(N=null),b===y&&(y._ignoreWhileAnimating=n),y.animateAll(function(){G("dragOverAnimationComplete"),y._ignoreWhileAnimating=null}),y!==b&&(b.animateAll(),b._ignoreWhileAnimating=null)),(n===c&&!c.animated||n===e&&!n.animated)&&(bt=null),!s.dragoverBubble&&!t.rootEl&&n!==document&&(c.parentNode[A]._isOutsideThisEl(t.target),!zt&&ut(t)),!s.dragoverBubble&&t.stopPropagation&&t.stopPropagation(),E=!0}function H(){B=X(c),nt=X(c,s.draggable),x({sortable:y,name:"change",toEl:e,newIndex:B,newDraggableIndex:nt,originalEvent:t})}if(t.preventDefault!==void 0&&t.cancelable&&t.preventDefault(),n=Y(n,s.draggable,e,!0),G("dragOver"),m.eventCanceled)return E;if(c.contains(t.target)||n.animated&&n.animatingX&&n.animatingY||y._ignoreWhileAnimating===n)return P(!1);if(Lt=!1,u&&!s.disabled&&(f?d||(a=C!==_):N===this||(this.lastPutMode=Yt.checkPull(this,u,c,t))&&l.checkPut(this,u,c,t))){if(D=this._getDirection(t,n)==="vertical",o=T(c),G("dragOverValid"),m.eventCanceled)return E;if(a)return C=_,Z(),this._hideClone(),G("revert"),m.eventCanceled||(st?_.insertBefore(c,st):_.appendChild(c)),P(!0);var R=ee(e,s.draggable);if(!R||mn(t,D,this)&&!R.animated){if(R===c)return P(!1);if(R&&e===t.target&&(n=R),n&&(r=T(n)),$t(_,e,c,o,n,r,t,!!n)!==!1)return Z(),R&&R.nextSibling?e.insertBefore(c,R.nextSibling):e.appendChild(c),C=e,H(),P(!0)}else if(R&&hn(t,D,this)){var ct=mt(e,0,s,!0);if(ct===c)return P(!1);if(n=ct,r=T(n),$t(_,e,c,o,n,r,t,!1)!==!1)return Z(),e.insertBefore(c,ct),C=e,H(),P(!0)}else if(n.parentNode===e){r=T(n);var U=0,dt,Ot=c.parentNode!==e,k=!ln(c.animated&&c.toRect||o,n.animated&&n.toRect||r,D),xt=D?"top":"left",it=Ie(n,"top","top")||Ie(c,"top","top"),Pt=it?it.scrollTop:void 0;bt!==n&&(dt=r[xt],Tt=!1,Ht=!k&&s.invertSwap||Ot),U=gn(t,n,r,D,k?1:s.swapThreshold,s.invertedSwapThreshold==null?s.swapThreshold:s.invertedSwapThreshold,Ht,bt===n);var Q;if(U!==0){var ft=X(c);do ft-=U,Q=C.children[ft];while(Q&&(h(Q,"display")==="none"||Q===g))}if(U===0||Q===n)return P(!1);bt=n,Ct=U;var At=n.nextElementSibling,ot=!1;ot=U===1;var Qt=$t(_,e,c,o,n,r,t,ot);if(Qt!==!1)return(Qt===1||Qt===-1)&&(ot=Qt===1),se=!0,setTimeout(fn,30),Z(),ot&&!At?e.appendChild(c):n.parentNode.insertBefore(c,ot?At:n),it&&xe(it,0,Pt-it.scrollTop),C=c.parentNode,dt!==void 0&&!Ht&&(Wt=Math.abs(dt-T(n)[xt])),H(),P(!0)}if(e.contains(c))return P(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){p(document,"mousemove",this._onTouchMove),p(document,"touchmove",this._onTouchMove),p(document,"pointermove",this._onTouchMove),p(document,"dragover",ut),p(document,"mousemove",ut),p(document,"touchmove",ut)},_offUpEvents:function(){var t=this.el.ownerDocument;p(t,"mouseup",this._onDrop),p(t,"touchend",this._onDrop),p(t,"pointerup",this._onDrop),p(t,"pointercancel",this._onDrop),p(t,"touchcancel",this._onDrop),p(document,"selectstart",this)},_onDrop:function(t){var e=this.el,n=this.options;if(B=X(c),nt=X(c,n.draggable),z("drop",this,{evt:t}),C=c&&c.parentNode,B=X(c),nt=X(c,n.draggable),m.eventCanceled){this._nulling();return}vt=!1,Ht=!1,Tt=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),ue(this.cloneId),ue(this._dragStartId),this.nativeDraggable&&(p(document,"drop",this),p(e,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),yt&&h(document.body,"user-select",""),h(c,"transform",""),t&&(St&&(t.cancelable&&t.preventDefault(),!n.dropBubble&&t.stopPropagation()),g&&g.parentNode&&g.parentNode.removeChild(g),(_===C||N&&N.lastPutMode!=="clone")&&S&&S.parentNode&&S.parentNode.removeChild(S),c&&(this.nativeDraggable&&p(c,"dragend",this),le(c),c.style["will-change"]="",St&&!vt&&M(c,N?N.options.ghostClass:this.options.ghostClass,!1),M(c,this.options.chosenClass,!1),x({sortable:this,name:"unchoose",toEl:C,newIndex:null,newDraggableIndex:null,originalEvent:t}),_!==C?(B>=0&&(x({rootEl:C,name:"add",toEl:C,fromEl:_,originalEvent:t}),x({sortable:this,name:"remove",toEl:C,originalEvent:t}),x({rootEl:C,name:"sort",toEl:C,fromEl:_,originalEvent:t}),x({sortable:this,name:"sort",toEl:C,originalEvent:t})),N&&N.save()):B!==pt&&B>=0&&(x({sortable:this,name:"update",toEl:C,originalEvent:t}),x({sortable:this,name:"sort",toEl:C,originalEvent:t})),m.active&&((B==null||B===-1)&&(B=pt,nt=_t),x({sortable:this,name:"end",toEl:C,originalEvent:t}),this.save()))),this._nulling()},_nulling:function(){z("nulling",this),_=c=C=g=st=S=Xt=et=lt=L=St=B=nt=pt=_t=bt=Ct=N=Yt=m.dragged=m.ghost=m.clone=m.active=null,jt.forEach(function(t){t.checked=!0}),jt.length=oe=re=0},handleEvent:function(t){switch(t.type){case"drop":case"dragend":this._onDrop(t);break;case"dragenter":case"dragover":c&&(this._onDragOver(t),dn(t));break;case"selectstart":t.preventDefault();break}},toArray:function(){for(var t=[],e,n=this.el.children,o=0,r=n.length,a=this.options;o<r;o++)e=n[o],Y(e,a.draggable,this.el,!1)&&t.push(e.getAttribute(a.dataIdAttr)||vn(e));return t},sort:function(t,e){var n={},o=this.el;this.toArray().forEach(function(r,a){var s=o.children[a];Y(s,this.options.draggable,o,!1)&&(n[r]=s)},this),e&&this.captureAnimationState(),t.forEach(function(r){n[r]&&(o.removeChild(n[r]),o.appendChild(n[r]))}),e&&this.animateAll()},save:function(){var t=this.options.store;t&&t.set&&t.set(this)},closest:function(t,e){return Y(t,e||this.options.draggable,this.el,!1)},option:function(t,e){var n=this.options;if(e===void 0)return n[t];var o=Dt.modifyOption(this,t,e);typeof o<"u"?n[t]=o:n[t]=e,t==="group"&&ke(n)},destroy:function(){z("destroy",this);var t=this.el;t[A]=null,p(t,"mousedown",this._onTapStart),p(t,"touchstart",this._onTapStart),p(t,"pointerdown",this._onTapStart),this.nativeDraggable&&(p(t,"dragover",this),p(t,"dragenter",this)),Array.prototype.forEach.call(t.querySelectorAll("[draggable]"),function(e){e.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Gt.splice(Gt.indexOf(this.el),1),this.el=t=null},_hideClone:function(){if(!et){if(z("hideClone",this),m.eventCanceled)return;h(S,"display","none"),this.options.removeCloneOnHide&&S.parentNode&&S.parentNode.removeChild(S),et=!0}},_showClone:function(t){if(t.lastPutMode!=="clone"){this._hideClone();return}if(et){if(z("showClone",this),m.eventCanceled)return;c.parentNode==_&&!this.options.group.revertClone?_.insertBefore(S,c):st?_.insertBefore(S,st):_.appendChild(S),this.options.group.revertClone&&this.animate(c,S),h(S,"display",""),et=!1}}};function dn(i){i.dataTransfer&&(i.dataTransfer.dropEffect="move"),i.cancelable&&i.preventDefault()}function $t(i,t,e,n,o,r,a,s){var l,u=i[A],f=u.options.onMove,d;return window.CustomEvent&&!K&&!wt?l=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(l=document.createEvent("Event"),l.initEvent("move",!0,!0)),l.to=t,l.from=i,l.dragged=e,l.draggedRect=n,l.related=o||t,l.relatedRect=r||T(t),l.willInsertAfter=s,l.originalEvent=a,i.dispatchEvent(l),f&&(d=f.call(u,l,a)),d}function le(i){i.draggable=!1}function fn(){se=!1}function hn(i,t,e){var n=T(mt(e.el,0,e.options,!0)),o=Ae(e.el,e.options,g),r=10;return t?i.clientX<o.left-r||i.clientY<n.top&&i.clientX<n.right:i.clientY<o.top-r||i.clientY<n.bottom&&i.clientX<n.left}function mn(i,t,e){var n=T(ee(e.el,e.options.draggable)),o=Ae(e.el,e.options,g),r=10;return t?i.clientX>o.right+r||i.clientY>n.bottom&&i.clientX>n.left:i.clientY>o.bottom+r||i.clientX>n.right&&i.clientY>n.top}function gn(i,t,e,n,o,r,a,s){var l=n?i.clientY:i.clientX,u=n?e.height:e.width,f=n?e.top:e.left,d=n?e.bottom:e.right,b=!1;if(!a){if(s&&Wt<u*o){if(!Tt&&(Ct===1?l>f+u*r/2:l<d-u*r/2)&&(Tt=!0),Tt)b=!0;else if(Ct===1?l<f+Wt:l>d-Wt)return-Ct}else if(l>f+u*(1-o)/2&&l<d-u*(1-o)/2)return pn(t)}return b=b||a,b&&(l<f+u*r/2||l>d-u*r/2)?l>f+u/2?1:-1:0}function pn(i){return X(c)<X(i)?1:-1}function vn(i){for(var t=i.tagName+i.className+i.src+i.href+i.textContent,e=t.length,n=0;e--;)n+=t.charCodeAt(e);return n.toString(36)}function bn(i){jt.length=0;for(var t=i.getElementsByTagName("input"),e=t.length;e--;){var n=t[e];n.checked&&jt.push(n)}}function Vt(i){return setTimeout(i,0)}function ue(i){return clearTimeout(i)}Ut&&v(document,"touchmove",function(i){(m.active||vt)&&i.cancelable&&i.preventDefault()}),m.utils={on:v,off:p,css:h,find:Te,is:function(t,e){return!!Y(t,e,t,!1)},extend:Je,throttle:Oe,closest:Y,toggleClass:M,clone:Pe,index:X,nextTick:Vt,cancelNextTick:ue,detectDirection:Re,getChild:mt,expando:A},m.get=function(i){return i[A]},m.mount=function(){for(var i=arguments.length,t=new Array(i),e=0;e<i;e++)t[e]=arguments[e];t[0].constructor===Array&&(t=t[0]),t.forEach(function(n){if(!n.prototype||!n.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(n));n.utils&&(m.utils=W(W({},m.utils),n.utils)),Dt.mount(n)})},m.create=function(i,t){return new m(i,t)},m.version=Ze;var I=[],It,ce,de=!1,fe,he,Kt,Nt;function wn(){function i(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var t in this)t.charAt(0)==="_"&&typeof this[t]=="function"&&(this[t]=this[t].bind(this))}return i.prototype={dragStarted:function(e){var n=e.originalEvent;this.sortable.nativeDraggable?v(document,"dragover",this._handleAutoScroll):this.options.supportPointer?v(document,"pointermove",this._handleFallbackAutoScroll):n.touches?v(document,"touchmove",this._handleFallbackAutoScroll):v(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(e){var n=e.originalEvent;!this.options.dragOverBubble&&!n.rootEl&&this._handleAutoScroll(n)},drop:function(){this.sortable.nativeDraggable?p(document,"dragover",this._handleAutoScroll):(p(document,"pointermove",this._handleFallbackAutoScroll),p(document,"touchmove",this._handleFallbackAutoScroll),p(document,"mousemove",this._handleFallbackAutoScroll)),Le(),Zt(),tn()},nulling:function(){Kt=ce=It=de=Nt=fe=he=null,I.length=0},_handleFallbackAutoScroll:function(e){this._handleAutoScroll(e,!0)},_handleAutoScroll:function(e,n){var o=this,r=(e.touches?e.touches[0]:e).clientX,a=(e.touches?e.touches[0]:e).clientY,s=document.elementFromPoint(r,a);if(Kt=e,n||this.options.forceAutoScrollFallback||wt||K||yt){me(e,this.options,s,n);var l=tt(s,!0);de&&(!Nt||r!==fe||a!==he)&&(Nt&&Le(),Nt=setInterval(function(){var u=tt(document.elementFromPoint(r,a),!0);u!==l&&(l=u,Zt()),me(e,o.options,u,n)},10),fe=r,he=a)}else{if(!this.options.bubbleScroll||tt(s,!0)===j()){Zt();return}me(e,this.options,tt(s,!1),!1)}}},$(i,{pluginName:"scroll",initializeByDefault:!0})}function Zt(){I.forEach(function(i){clearInterval(i.pid)}),I=[]}function Le(){clearInterval(Nt)}var me=Oe(function(i,t,e,n){if(t.scroll){var o=(i.touches?i.touches[0]:i).clientX,r=(i.touches?i.touches[0]:i).clientY,a=t.scrollSensitivity,s=t.scrollSpeed,l=j(),u=!1,f;ce!==e&&(ce=e,Zt(),It=t.scroll,f=t.scrollFn,It===!0&&(It=tt(e,!0)));var d=0,b=It;do{var D=b,y=T(D),E=y.top,G=y.bottom,Z=y.left,P=y.right,H=y.width,R=y.height,ct=void 0,U=void 0,dt=D.scrollWidth,Ot=D.scrollHeight,k=h(D),xt=D.scrollLeft,it=D.scrollTop;D===l?(ct=H<dt&&(k.overflowX==="auto"||k.overflowX==="scroll"||k.overflowX==="visible"),U=R<Ot&&(k.overflowY==="auto"||k.overflowY==="scroll"||k.overflowY==="visible")):(ct=H<dt&&(k.overflowX==="auto"||k.overflowX==="scroll"),U=R<Ot&&(k.overflowY==="auto"||k.overflowY==="scroll"));var Pt=ct&&(Math.abs(P-o)<=a&&xt+H<dt)-(Math.abs(Z-o)<=a&&!!xt),Q=U&&(Math.abs(G-r)<=a&&it+R<Ot)-(Math.abs(E-r)<=a&&!!it);if(!I[d])for(var ft=0;ft<=d;ft++)I[ft]||(I[ft]={});(I[d].vx!=Pt||I[d].vy!=Q||I[d].el!==D)&&(I[d].el=D,I[d].vx=Pt,I[d].vy=Q,clearInterval(I[d].pid),(Pt!=0||Q!=0)&&(u=!0,I[d].pid=setInterval((function(){n&&this.layer===0&&m.active._onTouchMove(Kt);var At=I[this.layer].vy?I[this.layer].vy*s:0,ot=I[this.layer].vx?I[this.layer].vx*s:0;typeof f=="function"&&f.call(m.dragged.parentNode[A],ot,At,i,Kt,I[this.layer].el)!=="continue"||xe(I[this.layer].el,ot,At)}).bind({layer:d}),24))),d++}while(t.bubbleScroll&&b!==l&&(b=tt(b,!1)));de=u}},30),Ge=function(t){var e=t.originalEvent,n=t.putSortable,o=t.dragEl,r=t.activeSortable,a=t.dispatchSortableEvent,s=t.hideGhostForTarget,l=t.unhideGhostForTarget;if(e){var u=n||r;s();var f=e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:e,d=document.elementFromPoint(f.clientX,f.clientY);l(),u&&!u.el.contains(d)&&(a("spill"),this.onSpill({dragEl:o,putSortable:n}))}};function ge(){}ge.prototype={startIndex:null,dragStart:function(t){var e=t.oldDraggableIndex;this.startIndex=e},onSpill:function(t){var e=t.dragEl,n=t.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var o=mt(this.sortable.el,this.startIndex,this.options);o?this.sortable.el.insertBefore(e,o):this.sortable.el.appendChild(e),this.sortable.animateAll(),n&&n.animateAll()},drop:Ge},$(ge,{pluginName:"revertOnSpill"});function pe(){}pe.prototype={onSpill:function(t){var e=t.dragEl,n=t.putSortable,o=n||this.sortable;o.captureAnimationState(),e.parentNode&&e.parentNode.removeChild(e),o.animateAll()},drop:Ge},$(pe,{pluginName:"removeOnSpill"}),m.mount(new wn),m.mount(pe,ge);class yn extends je{oninit(t){super.oninit(t),this.loading=!1,this.buttonsCustomizationList=[],this.loadResults()}initSort(){const t=document.getElementById("buttonsCustomizationSortableItems");t&&m.create(t,{animation:150,swapThreshold:.65,onEnd:e=>this.updateSort(e)})}content(t){const e=Ft||w.m||window.m;return e?e("div",{className:"ExtensionPage-settings FlarumBadgesPage"},[e("div",{className:"container"},[e("div",{style:"padding-bottom:10px"},[e(rt,{className:"Button",onclick:()=>w.modal.show(Mt)},w.translator.trans("client1-buttons-customization.admin.link-add"))]),e("ul",{id:"buttonsCustomizationSortableItems",style:"padding:0px;list-style-type: none;",oncreate:this.initSort.bind(this)},this.buttonsCustomizationList.map(n=>e("li",{"data-item-id":n.id(),style:"margin-top:5px;background: var(--body-bg);"},e(qe,{ButtonsCustomizationItemData:n}))))])]):(console.error("Mithril is not available"),"Error: Mithril not loaded")}updateSort(t){const e=t.newIndex,n=t.oldIndex;if(e!==n){const o=t.from?.children,r={};if(o){for(let a=0;a<o.length;a++){const l=o[a].getAttribute("data-item-id");l&&(r[l]=a)}w.request({url:`${w.forum.attribute("apiUrl")}/buttonsCustomizationList/order`,method:"POST",body:{buttonsCustomizationOrder:r}})}}}parseResults(t){return this.buttonsCustomizationList.push(...t),w&&w.m&&w.m.redraw?w.m.redraw():Ft&&Ft.redraw&&Ft.redraw(),t}loadResults(){return w.store.find("buttonsCustomizationList").catch(()=>[]).then(this.parseResults.bind(this))}}class He extends at{}Object.assign(He.prototype,{id:at.attribute("id"),name:at.attribute("name"),icon:at.attribute("icon"),color:at.attribute("color"),url:at.attribute("url"),sort:at.attribute("sort")}),w.initializers.add("wusong8899-buttons-customization",()=>{w.store.models.buttonsCustomizationList=He,w.extensionData.for("wusong8899-client1-buttons-customization").registerPage(yn)})})(flarum.core.compat["admin/app"],flarum.core.compat["admin/components/ExtensionPage"],flarum.core.compat["common/components/Button"],flarum.core.compat["common/components/Modal"],flarum.core.compat["common/utils/Stream"],flarum.core.compat["common/Component"],flarum.core.compat.mithril,flarum.core.compat["common/Model"]);
//# sourceMappingURL=admin.js.map

module.exports={};